
import { useState, useEffect } from 'react';
import Hero from '../components/Hero';
import Services from '../components/Services';
import Team from '../components/Team';
import Contact from '../components/Contact';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';

const Index = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-premium-gradient overflow-x-hidden">
      <Navigation scrollToSection={scrollToSection} />

      {/* Enhanced Floating Logo */}
      <div className="fixed top-8 left-8 z-50 animate-fade-in">
        <div className="glass-morphism-premium p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-xl logo-glow group">
          <img
            src="/lovable-uploads/25e49309-a0c5-454c-bb44-9296c10e2397.png"
            alt="SUZ Reinigung Logo"
            className="w-16 h-16 object-contain transition-all duration-300 group-hover:scale-110 min-w-[40px] min-h-[40px]"
          />
        </div>
      </div>

      <Hero scrollY={scrollY} />
      <Services />
      <Team />
      <Contact />
      <Footer />
    </div>
  );
};

export default Index;
