
import { Mail, Phone, Users } from 'lucide-react';

const Contact = () => {
  return (
    <section id="contact" className="py-24 px-4">
      <div className="max-w-5xl mx-auto text-center">
        <div className="glass-morphism-premium p-16 rounded-3xl border border-white/30 animate-fade-in shadow-2xl">
          <h2 className="text-6xl font-light text-slate-800 mb-8">
            <span className="gradient-text">Kontakt</span> aufnehmen
          </h2>
          <p className="text-2xl text-slate-600 mb-12">
            Kontaktieren Sie uns jetzt – schnell & unkompliziert!
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-8 mb-16">
            <a
              href="https://wa.me/4917623152477"
              target="_blank"
              rel="noopener noreferrer"
              className="premium-button-3d bg-gradient-to-r from-green-500 to-green-600 text-white px-12 py-6 rounded-full text-xl font-semibold hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-3"
            >
              <Phone className="w-6 h-6" />
              WhatsApp
            </a>
            <a
              href="mailto:<EMAIL>"
              className="premium-button-3d bg-gradient-to-r from-blue-500 to-blue-600 text-white px-12 py-6 rounded-full text-xl font-semibold hover:scale-105 transition-all duration-300 shadow-2xl flex items-center justify-center gap-3"
            >
              <Mail className="w-6 h-6" />
              E-Mail
            </a>
          </div>

          <div className="grid md:grid-cols-3 gap-8 text-slate-600">
            <div className="flex flex-col items-center">
              <Phone className="w-8 h-8 text-blue-500 mb-4" />
              <h3 className="font-semibold text-slate-800 mb-3 text-lg">Telefon</h3>
              <p className="text-lg">+49 176 23152477</p>
            </div>
            <div className="flex flex-col items-center">
              <Mail className="w-8 h-8 text-blue-500 mb-4" />
              <h3 className="font-semibold text-slate-800 mb-3 text-lg">E-Mail</h3>
              <p className="text-lg"><EMAIL></p>
            </div>
            <div className="flex flex-col items-center">
              <Users className="w-8 h-8 text-blue-500 mb-4" />
              <h3 className="font-semibold text-slate-800 mb-3 text-lg">Adresse</h3>
              <p className="text-lg">Paul-Langen-Straße 39<br />53229 Bonn</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
