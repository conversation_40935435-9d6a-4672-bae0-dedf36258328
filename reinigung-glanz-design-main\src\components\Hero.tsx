
import { useState, useEffect } from 'react';

interface HeroProps {
  scrollY: number;
}

const Hero = ({ scrollY }: HeroProps) => {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center px-4 relative">
      <div 
        className="text-center max-w-5xl mx-auto animate-fade-in"
        style={{
          transform: `translateY(${scrollY * 0.2}px)`,
        }}
      >
        <h1 className="text-7xl md:text-8xl font-light text-slate-800 mb-12 leading-tight">
          <span className="gradient-text-animated pulse-glow">Reinigung</span> neu definiert
        </h1>
        <p className="text-2xl md:text-3xl text-slate-600 mb-6 font-light leading-relaxed">
          mit Stil, Präzision und Vertrauen
        </p>
        <p className="text-xl text-slate-500 mb-16 max-w-3xl mx-auto leading-relaxed">
          Zuverlässige Reinigung für Hotels, Büros und Wohnanlagen – von Prof<PERSON> für Profis.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <a
            href="https://wa.me/4917623152477"
            target="_blank"
            rel="noopener noreferrer"
            className="premium-button bg-gradient-to-r from-green-500 to-green-600 text-white px-10 py-5 rounded-full font-semibold hover:scale-105 transition-all duration-300 shadow-2xl text-lg"
          >
            WhatsApp Kontakt
          </a>
          <a
            href="mailto:<EMAIL>"
            className="premium-button bg-gradient-to-r from-blue-500 to-blue-600 text-white px-10 py-5 rounded-full font-semibold hover:scale-105 transition-all duration-300 shadow-2xl text-lg"
          >
            E-Mail senden
          </a>
        </div>
      </div>

      {/* Enhanced Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="floating-element absolute top-20 left-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
        <div className="floating-element absolute top-40 right-20 w-32 h-32 bg-cyan-300/20 rounded-full blur-2xl" style={{animationDelay: '2s'}}></div>
        <div className="floating-element absolute bottom-20 left-1/4 w-48 h-48 bg-sky-300/20 rounded-full blur-3xl" style={{animationDelay: '4s'}}></div>
      </div>
    </section>
  );
};

export default Hero;
