
const Services = () => {
  const services = [
    {
      title: "Hotelzimmerreinigung",
      description: "Tiefenreinigung und tägliche Pflege für höchste Hygienestandards in Hotelzimmern.",
      icon: "🏨"
    },
    {
      title: "Teppichreinigung", 
      description: "Tiefenreinigung für Teppiche und Polster. Wir entfernen Flecken, Gerüche und Allergene für ein frisches und hygienisches Raumklima.",
      icon: "🏠"
    },
    {
      title: "Bodenreinigung",
      description: "Professionelle Pflege für Hartböden, Fliesen, Laminat und mehr. Wir sorgen für glänzende, hygienisch saubere Oberflächen.",
      icon: "✨"
    },
    {
      title: "Gemeinschaftsräume",
      description: "Zuverlässige Reinigung von T<PERSON>nhäusern, Fluren und Gemeinschaftsbereichen für Mehrfamilienhäuser und Wohnanlagen.",
      icon: "🏢"
    },
    {
      title: "Büroreinigung",
      description: "Professionelle Reinigung von Büroflächen und Arbeitsplätzen für ein sauberes und produktives Arbeitsumfeld.",
      icon: "💼"
    },
    {
      title: "Desinfektion",
      description: "Gründliche Desinfektion von Räumen und Oberflächen zur Bekämpfung von Keimen, Bakterien und Viren für maximale Hygiene und Sicherheit.",
      icon: "🧽"
    }
  ];

  return (
    <section id="services" className="py-24 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-20 animate-fade-in">
          <h2 className="text-6xl font-light text-slate-800 mb-8">
            Unsere <span className="gradient-text">Leistungen</span>
          </h2>
          <p className="text-2xl text-slate-600 max-w-3xl mx-auto">
            Professionelle Reinigungslösungen für jeden Bedarf
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {services.map((service, index) => (
            <div 
              key={index}
              className="service-card-premium glass-morphism-premium p-10 rounded-3xl border border-white/30 hover:scale-105 transition-all duration-500 animate-fade-in group shadow-2xl"
              style={{animationDelay: `${index * 0.15}s`}}
            >
              <div className="icon-badge mb-6">
                <div className="text-5xl group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>
              </div>
              <h3 className="text-2xl font-semibold text-slate-800 mb-6">
                {service.title}
              </h3>
              <p className="text-slate-600 leading-relaxed text-lg">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
