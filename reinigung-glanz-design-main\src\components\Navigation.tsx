
interface NavigationProps {
  scrollToSection: (id: string) => void;
}

const Navigation = ({ scrollToSection }: NavigationProps) => {
  return (
    <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in">
      <div className="glass-morphism-premium px-8 py-4 rounded-full border border-white/30 shadow-xl backdrop-blur-xl">
        <div className="flex items-center space-x-10">
          <button 
            onClick={() => scrollToSection('home')}
            className="text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105"
          >
            Startseite
          </button>
          <button 
            onClick={() => scrollToSection('services')}
            className="text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105"
          >
            Leistungen
          </button>
          <button 
            onClick={() => scrollToSection('team')}
            className="text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105"
          >
            Unser Team
          </button>
          <button 
            onClick={() => scrollToSection('contact')}
            className="text-slate-700 hover:text-blue-600 transition-all duration-300 font-medium text-base hover:scale-105"
          >
            Kontakt
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
